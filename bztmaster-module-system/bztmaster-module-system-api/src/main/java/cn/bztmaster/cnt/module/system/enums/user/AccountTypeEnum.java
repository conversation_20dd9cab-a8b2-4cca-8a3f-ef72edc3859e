package cn.bztmaster.cnt.module.system.enums.user;

import cn.bztmaster.cnt.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 账户类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AccountTypeEnum implements ArrayValuable<Integer> {

    /**
     * 内部员工
     */
    INTERNAL_EMPLOYEE(1, "内部员工"),
    /**
     * 企业用户
     */
    ENTERPRISE_USER(2, "企业用户");

    public static final Integer[] ARRAYS = Arrays.stream(values()).map(AccountTypeEnum::getType).toArray(Integer[]::new);

    /**
     * 类型
     */
    private final Integer type;
    /**
     * 类型名
     */
    private final String name;

    @Override
    public Integer[] array() {
        return ARRAYS;
    }

    /**
     * 根据类型获取枚举
     *
     * @param type 类型
     * @return 枚举
     */
    public static AccountTypeEnum valueOf(Integer type) {
        for (AccountTypeEnum accountType : values()) {
            if (accountType.getType().equals(type)) {
                return accountType;
            }
        }
        return null;
    }

    /**
     * 判断是否为合作伙伴类型（企业用户）
     *
     * @param type 类型
     * @return 是否为合作伙伴类型
     */
    public static boolean isPartner(Integer type) {
        return ENTERPRISE_USER.getType().equals(type);
    }
}
