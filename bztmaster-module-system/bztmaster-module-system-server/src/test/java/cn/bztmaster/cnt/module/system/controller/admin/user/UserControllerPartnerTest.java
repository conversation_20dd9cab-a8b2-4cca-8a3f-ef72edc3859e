package cn.bztmaster.cnt.module.system.controller.admin.user;

import cn.bztmaster.cnt.framework.test.core.ut.BaseDbUnitTest;
import cn.bztmaster.cnt.module.system.controller.admin.user.vo.user.UserPageReqVO;
import cn.bztmaster.cnt.module.system.controller.admin.user.vo.user.UserRespVO;
import cn.bztmaster.cnt.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.bztmaster.cnt.module.system.convert.user.UserConvert;
import cn.bztmaster.cnt.module.system.dal.dataobject.dept.DeptDO;
import cn.bztmaster.cnt.module.system.dal.dataobject.user.AdminUserDO;
import cn.bztmaster.cnt.module.system.enums.user.AccountTypeEnum;
import cn.bztmaster.cnt.module.system.service.dept.DeptService;
import cn.bztmaster.cnt.module.system.service.user.AdminUserService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 用户控制器合作伙伴功能测试
 *
 * <AUTHOR>
 */
@Import({UserController.class})
public class UserControllerPartnerTest extends BaseDbUnitTest {

    @Resource
    private UserController userController;

    @MockBean
    private AdminUserService userService;
    @MockBean
    private DeptService deptService;

    @Test
    public void testUserSaveReqVO_PartnerValidation() {
        // 测试合作伙伴类型用户的校验逻辑
        UserSaveReqVO reqVO = new UserSaveReqVO();
        reqVO.setUsername("testuser");
        reqVO.setNickname("测试用户");
        reqVO.setPassword("123456");
        reqVO.setAccountType(AccountTypeEnum.ENTERPRISE_USER.getType());
        
        // 测试合作伙伴类型但未填写合作伙伴信息的情况
        reqVO.setPartnerId(null);
        reqVO.setPartnerName(null);
        assertFalse(reqVO.isPartnerInfoValid(), "合作伙伴类型用户必须填写合作伙伴信息");
        
        // 测试合作伙伴类型且填写了合作伙伴信息的情况
        reqVO.setPartnerId(1001L);
        reqVO.setPartnerName("测试合作伙伴");
        assertTrue(reqVO.isPartnerInfoValid(), "合作伙伴类型用户填写了合作伙伴信息应该通过校验");
        
        // 测试内部员工类型的情况
        reqVO.setAccountType(AccountTypeEnum.INTERNAL_EMPLOYEE.getType());
        reqVO.setPartnerId(null);
        reqVO.setPartnerName(null);
        assertTrue(reqVO.isPartnerInfoValid(), "内部员工类型不需要填写合作伙伴信息");
    }

    @Test
    public void testUserPageReqVO_PartnerFields() {
        // 测试用户分页查询VO的合作伙伴字段
        UserPageReqVO pageReqVO = new UserPageReqVO();
        pageReqVO.setPartnerId(1001L);
        pageReqVO.setPartnerName("测试合作伙伴");
        
        assertNotNull(pageReqVO.getPartnerId(), "分页查询应该支持按合作伙伴ID筛选");
        assertNotNull(pageReqVO.getPartnerName(), "分页查询应该支持按合作伙伴名称筛选");
        assertEquals(1001L, pageReqVO.getPartnerId());
        assertEquals("测试合作伙伴", pageReqVO.getPartnerName());
    }

    @Test
    public void testUserConvert_AccountTypeName() {
        // 测试用户转换器的账户类型名称转换
        AdminUserDO userDO = new AdminUserDO();
        userDO.setId(1L);
        userDO.setUsername("testuser");
        userDO.setNickname("测试用户");
        userDO.setAccountType(AccountTypeEnum.ENTERPRISE_USER.getType());
        userDO.setPartnerId(1001L);
        userDO.setPartnerName("测试合作伙伴");
        
        DeptDO deptDO = new DeptDO();
        deptDO.setId(1L);
        deptDO.setName("测试部门");
        
        UserRespVO userRespVO = UserConvert.INSTANCE.convert(userDO, deptDO);
        
        assertNotNull(userRespVO, "转换结果不应该为空");
        assertEquals(AccountTypeEnum.ENTERPRISE_USER.getType(), userRespVO.getAccountType());
        assertEquals(AccountTypeEnum.ENTERPRISE_USER.getName(), userRespVO.getAccountTypeName());
        assertEquals(1001L, userRespVO.getPartnerId());
        assertEquals("测试合作伙伴", userRespVO.getPartnerName());
        assertEquals("测试部门", userRespVO.getDeptName());
    }

    @Test
    public void testAccountTypeEnum() {
        // 测试账户类型枚举
        assertEquals(1, AccountTypeEnum.INTERNAL_EMPLOYEE.getType());
        assertEquals("内部员工", AccountTypeEnum.INTERNAL_EMPLOYEE.getName());
        assertEquals(2, AccountTypeEnum.ENTERPRISE_USER.getType());
        assertEquals("企业用户", AccountTypeEnum.ENTERPRISE_USER.getName());
        
        // 测试isPartner方法
        assertTrue(AccountTypeEnum.isPartner(AccountTypeEnum.ENTERPRISE_USER.getType()));
        assertFalse(AccountTypeEnum.isPartner(AccountTypeEnum.INTERNAL_EMPLOYEE.getType()));
        assertFalse(AccountTypeEnum.isPartner(null));
        
        // 测试valueOf方法
        assertEquals(AccountTypeEnum.INTERNAL_EMPLOYEE, AccountTypeEnum.valueOf(1));
        assertEquals(AccountTypeEnum.ENTERPRISE_USER, AccountTypeEnum.valueOf(2));
        assertNull(AccountTypeEnum.valueOf(999));
    }
}
