package cn.bztmaster.cnt.module.publicbiz.api.aunt;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.AuntOrderDetailDTO;
import cn.bztmaster.cnt.module.publicbiz.api.aunt.dto.AuntOrderListDTO;

/**
 * 阿姨订单 API 接口
 *
 * <AUTHOR>
 */
public interface AuntOrderApi {

    /**
     * 获得阿姨订单列表
     *
     * @param auntOneId 阿姨OneID
     * @param orderStatus 订单状态
     * @param customerName 客户姓名
     * @param serviceAddress 服务地址
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 订单列表
     */
    CommonResult<PageResult<AuntOrderListDTO>> getOrderList(String auntOneId, String orderStatus, 
                                                           String customerName, String serviceAddress,
                                                           Integer pageNo, Integer pageSize);

    /**
     * 获得阿姨订单详情
     *
     * @param orderId 订单ID
     * @param auntOneId 阿姨OneID
     * @return 订单详情
     */
    CommonResult<AuntOrderDetailDTO> getOrderDetail(Long orderId, String auntOneId);

    /**
     * 确认订单
     *
     * @param orderId 订单ID
     * @param auntOneId 阿姨OneID
     * @return 操作结果
     */
    CommonResult<Boolean> confirmOrder(Long orderId, String auntOneId);

    /**
     * 拒绝订单
     *
     * @param orderId 订单ID
     * @param auntOneId 阿姨OneID
     * @param rejectReason 拒绝原因
     * @return 操作结果
     */
    CommonResult<Boolean> rejectOrder(Long orderId, String auntOneId, String rejectReason);
} 